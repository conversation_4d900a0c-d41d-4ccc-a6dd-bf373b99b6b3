/**
 * user-tracking-request controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::user-tracking-request.user-tracking-request', ({ strapi }) => ({
  // Get user stats
  async getUserStats(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      const userTrackingService = strapi.service('api::user-tracking-request.user-tracking-request');
      const stats = await userTrackingService.getUserStats(user.id);

      ctx.body = {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('Error getting user stats:', error);
      ctx.badRequest('Error getting user stats');
    }
  },

  // Manual daily reset (admin only)
  async manualDailyReset(ctx) {
    try {
      const { user } = ctx.state;
      if (!user) {
        return ctx.unauthorized('Authentication required');
      }

      // Check if user is admin (you may want to add proper admin role check)
      const isAdmin = user.role?.type === 'admin' || user.role?.name === 'Admin';
      if (!isAdmin) {
        return ctx.forbidden('Admin access required');
      }

      const { userId } = ctx.request.body;
      if (!userId) {
        return ctx.badRequest('userId is required');
      }

      const userTrackingService = strapi.service('api::user-tracking-request.user-tracking-request');
      const result = await userTrackingService.performManualDailyReset(userId);

      ctx.body = result;
    } catch (error) {
      console.error('Error performing manual daily reset:', error);
      ctx.badRequest('Error performing manual daily reset');
    }
  },

  // Update daily request limits for all basic tier users (no authentication required)
  async updateBasicDailyLimits(ctx) {
    try {
      const { newDailyLimit } = ctx.request.body;

      if (!newDailyLimit || typeof newDailyLimit !== 'number' || newDailyLimit < 0) {
        return ctx.badRequest('Valid newDailyLimit (number >= 0) is required');
      }

      const userTrackingService = strapi.service('api::user-tracking-request.user-tracking-request');
      const result = await userTrackingService.updateBasicUsersDailyLimits(newDailyLimit);

      ctx.body = {
        success: true,
        message: `Updated daily limits for ${result.updatedCount} basic tier users`,
        data: {
          updatedCount: result.updatedCount,
          newDailyLimit: newDailyLimit,
          updatedUsers: result.updatedUsers || []
        }
      };
    } catch (error) {
      console.error('Error updating basic users daily limits:', error);
      ctx.badRequest('Error updating basic users daily limits');
    }
  }
}));
